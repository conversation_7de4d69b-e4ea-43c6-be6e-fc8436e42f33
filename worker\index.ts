//@ts-ignore
import pagesWorker from "../dist/_worker.js/index.js";
import { Env } from "../functions/types";
import moment from 'moment-timezone';
// 导入 cron 任务处理器
import { onRequest as resetAccountsCron } from '../functions/newaccount/reset-accounts-cron';
import { onRequest as initAccountsCron } from '../functions/newaccount/init-accounts-cron';


export default {
  async fetch(request: Request, env: Env, ctx: ExecutionContext): Promise<Response> {
    // 将请求转发给 Pages Functions worker
    // Pages Functions 期望的 workerContext 参数需要有 waitUntil 方法
    const workerContext = {
      ...ctx,
      waitUntil: ctx.waitUntil.bind(ctx),
      passThroughOnException: () => { }
    };
    return pagesWorker.fetch(request, env, workerContext);
  },

  /**
 * Cloudflare Workers 调度事件处理器
 * 根据 cron 表达式执行相应的任务
 * http://localhost:8787/__scheduled?cron=0 8 * * *
 * http://localhost:8787/cdn-cgi/handler/scheduled?cron=*+*+*+*+*
 */
  async scheduled(controller: ScheduledController, env: Env, ctx: ExecutionContext): Promise<void> {
    try {
      const currentTime = moment().tz('Asia/Shanghai');
      const formattedTime = currentTime.format('YYYY-MM-DD HH:mm:ss');
      const cronExpression = controller.cron;

      console.log(`[Scheduled Event] Triggered at ${formattedTime} with cron: ${cronExpression}`);

      // 创建模拟的 EventContext 用于调用 cron 任务
      const createMockContext = (taskType: 'reset' | 'init'): EventContext<Env, string, Record<string, unknown>> => ({
        request: new Request(`https://seedlog.godgodgame.com/newaccount/${taskType}-accounts-cron`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }),
        env,
        params: {},
        data: {},
        waitUntil: ctx.waitUntil.bind(ctx),
        passThroughOnException: () => { },
        next: async () => new Response('Not implemented'),
        functionPath: `/newaccount/${taskType}-accounts-cron`
      });

      let taskResult: Response | null = null;
      let taskName = '';

      // 使用 switch 表达式根据 cron 表达式执行不同的任务
      switch (cronExpression) {
        case "0 6 * * *":
        case "0 8 * * *":
        case "0 10 * * *":
        case "0 12 * * *":
        case "0 14 * * *":
        case "0 16 * * *":
          // 每天早上8点执行重置账号任务
          taskName = 'reset-accounts';
          console.log('[Scheduled Event] Executing reset accounts cron task');
          try {
            taskResult = await resetAccountsCron(createMockContext('reset'));
            console.log('[Scheduled Event] Reset accounts cron task completed');
          } catch (error) {
            console.error('[Scheduled Event] Reset accounts cron task failed:', error);
          }
          break;

        case "0 20 * * *":
          // 每天晚上20点执行初始化账号任务
          taskName = 'init-accounts';
          console.log('[Scheduled Event] Executing init accounts cron task');
          try {
            taskResult = await initAccountsCron(createMockContext('init'));
            console.log('[Scheduled Event] Init accounts cron task completed');
          } catch (error) {
            console.error('[Scheduled Event] Init accounts cron task failed:', error);
          }
          break;

        case "* * * * *":
          // 每分钟执行一次的测试任务
          taskName = 'test-scheduler';
          console.log('[Scheduled Event] Executing test scheduler task');
          try {
            console.log('[Scheduled Event] Test scheduler task completed');
          } catch (error) {
            console.error('[Scheduled Event] Test scheduler task failed:', error);
          }
          break;

        default:
          console.log(`[Scheduled Event] No handler configured for cron expression: ${cronExpression}`);
          return;
      }

      // 记录执行结果
      if (taskResult) {
        const resultText = await taskResult.text();
        console.log(`[Scheduled Event] Task ${taskName} result (${taskResult.status}):`, resultText);
      }

    } catch (error) {
      console.error('[Scheduled Event] Error in scheduled handler:', error);
      // 不抛出错误，避免影响其他调度任务
    }
  }
};